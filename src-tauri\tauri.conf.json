{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON> Shuffler", "version": "0.1.0", "identifier": "com.lightning-shuffler.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON> Shuffler", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "decorations": true, "transparent": false, "alwaysOnTop": false, "skipTaskbar": false}], "security": {"csp": null}, "trayIcon": {"iconPath": "icons/32x32.png", "iconAsTemplate": false, "menuOnLeftClick": false, "tooltip": "<PERSON> Shuffler"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}