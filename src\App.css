/* <PERSON> Shuffler Styles */

/* CSS Variables */
:root {
  /* Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --border-primary: #374151;
  --border-secondary: #4B5563;
  --text-primary: #ffffff;
  --text-secondary: #9CA3AF;
  --neon-green: #00ff00;
  --neon-green-dark: #00cc00;
  --danger: #ff4444;
  --danger-dark: #cc0000;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* Typography */
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
}

/* Glassmorphism effect */
.glass {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neon glow effects */
.neon-glow {
  box-shadow: 0 0 5px #00ff00, 0 0 10px #00ff00, 0 0 15px #00ff00;
}

.neon-glow-strong {
  box-shadow: 0 0 10px #00ff00, 0 0 20px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(42, 42, 42, 0.5);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #00ff00;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #00cc00;
}

/* Video player gradient overlay */
.video-gradient {
  background: linear-gradient(45deg,
      rgba(0, 255, 0, 0.1) 0%,
      rgba(0, 255, 0, 0.05) 50%,
      rgba(0, 255, 0, 0.1) 100%);
}

/* Lightning bolt animation */
@keyframes lightning {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

/* Spin animation for loading */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Fade in animation */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from left */
@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for buttons */
@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

/* Glow animation */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px #00ff00;
  }

  50% {
    box-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00;
  }

  100% {
    box-shadow: 0 0 5px #00ff00;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.pulse-on-hover:hover {
  animation: pulse 0.3s ease-in-out;
}

.glow-effect {
  animation: glow 2s ease-in-out infinite;
}

.lightning-pulse {
  animation: lightning 2s ease-in-out infinite;
}

/* Shuffle animation for cards */
.shuffle-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.shuffle-card.shuffling {
  animation: shuffle 0.6s ease-in-out;
}

/* Button hover effects */
.btn-neon {
  background: linear-gradient(45deg, #00ff00, #00cc00);
  border: none;
  color: #000;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-neon:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 0, 0.4);
}

.btn-neon:active {
  transform: translateY(0);
}

/* Search bar focus effect */
.search-focus {
  border-color: var(--neon-green);
  box-shadow: 0 0 0 3px rgba(0, 255, 0, 0.1);
}

/* =============================================================================
   COMPONENT STYLES
   ============================================================================= */

/* Button Components */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  position: relative;
  overflow: hidden;
}

.btn-base:disabled,
.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
}

.btn-md {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 14px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 16px;
}

.btn-primary {
  background: linear-gradient(45deg, var(--neon-green), var(--neon-green-dark));
  color: #000;
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-secondary);
}

.btn-danger {
  background: linear-gradient(45deg, var(--danger), var(--danger-dark));
  color: var(--text-primary);
}

.btn-ghost {
  background: transparent;
  color: var(--text-primary);
}

.btn-ghost:hover {
  background: var(--bg-tertiary);
}

/* Loading spinner in buttons */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  opacity: 0.7;
}

/* Input Components */
.input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.input-wrapper {
  position: relative;
}

.input-base {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  outline: none;
  transition: all 0.2s ease;
}

.input-base:focus {
  border-color: var(--neon-green);
  box-shadow: 0 0 0 3px rgba(0, 255, 0, 0.1);
}

.input-with-icon-left {
  padding-left: 40px;
}

.input-with-icon-right {
  padding-right: 40px;
}

.input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  width: 16px;
  height: 16px;
}

.input-icon-left {
  left: 12px;
}

.input-icon-right {
  right: 12px;
}

.input-error {
  border-color: var(--danger);
}

.input-error-message {
  font-size: 12px;
  color: var(--danger);
}

/* Modal Components */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  margin: var(--spacing-md);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
}

/* Notification Components */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  z-index: 1100;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-width: 200px;
}

.notification-success {
  background: var(--neon-green);
  color: #000;
}

.notification-error {
  background: var(--danger);
  color: var(--text-primary);
}

.notification-icon {
  width: 20px;
  height: 20px;
}

.notification-message {
  flex: 1;
}

/* Loading Components */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
}

.loading-text {
  font-size: 18px;
  color: var(--text-secondary);
}

.text-neon-green {
  color: var(--neon-green);
}

/* Header Components */
.header-container {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.header-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-logo {
  width: 32px;
  height: 32px;
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.header-help-button {
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
}

.header-help-icon {
  width: 20px;
  height: 20px;
}

.header-add-playlist {
  margin-top: var(--spacing-md);
}

.add-playlist-form {
  display: flex;
  gap: var(--spacing-sm);
}

.add-playlist-input {
  flex: 1;
}

.add-playlist-button {
  white-space: nowrap;
}

.add-playlist-icon {
  width: 16px;
  height: 16px;
}

/* Video Player Components */
.video-player-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.welcome-content {
  text-align: center;
}

.welcome-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-md);
  color: var(--neon-green);
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.welcome-subtitle {
  color: var(--text-secondary);
  margin: 0;
}

.video-player-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.video-player-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
}

.video-gradient-border {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  background: linear-gradient(45deg, rgba(0, 255, 0, 0.1) 0%, rgba(0, 255, 0, 0.05) 50%, rgba(0, 255, 0, 0.1) 100%);
  padding: 4px;
  width: 100%;
  max-width: 900px;
}

.video-inner {
  border-radius: var(--radius-md);
  overflow: hidden;
}

.youtube-player {
  width: 100%;
}

.video-info {
  text-align: center;
  padding: 0 var(--spacing-lg) var(--spacing-md);
  border-bottom: 1px solid var(--border-primary);
}

.video-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-author {
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
  font-size: 14px;
}

/* Controls Components */
.controls-container {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-primary);
}

.controls-main {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
}

.control-button {
  padding: var(--spacing-md);
  border-radius: 50%;
}

.control-button-primary {
  padding: var(--spacing-md);
  border-radius: 50%;
}

.control-icon {
  width: 24px;
  height: 24px;
}

.control-icon-primary {
  width: 32px;
  height: 32px;
}

.loop-button {
  position: relative;
}

.loop-counter {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--neon-green);
  color: #000;
  font-size: 12px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.volume-icon {
  width: 20px;
  height: 20px;
}

.volume-slider {
  width: 80px;
  accent-color: var(--neon-green);
}

/* Sidebar Components */
.sidebar-container {
  width: 320px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.sidebar-search {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-primary);
}

.search-bar-container {
  width: 100%;
}

.search-input {
  width: 100%;
}

.search-icon {
  width: 16px;
  height: 16px;
}

.sidebar-section {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-primary);
}

.sidebar-section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.sidebar-empty-message {
  font-size: 14px;
  color: var(--text-secondary);
  text-align: center;
  padding: var(--spacing-md) 0;
  margin: 0;
}

.sidebar-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.sidebar-queue {
  flex: 1;
  overflow-y: auto;
}

.sidebar-queue-content {
  flex: 1;
  overflow-y: auto;
}

.queue-title {
  font-size: 18px;
}

.queue-icon {
  width: 20px;
  height: 20px;
  color: var(--neon-green);
}

.queue-items {
  max-height: none;
}

.sidebar-no-results {
  text-align: center;
  padding: var(--spacing-xl) 0;
  color: var(--text-secondary);
}

.sidebar-no-results p {
  margin: 0;
}

.sidebar-no-results-hint {
  font-size: 14px;
  margin-top: var(--spacing-xs) !important;
}

/* Custom scrollbar for sidebar */
.sidebar-queue-content::-webkit-scrollbar {
  width: 8px;
}

.sidebar-queue-content::-webkit-scrollbar-track {
  background: rgba(42, 42, 42, 0.5);
  border-radius: 4px;
}

.sidebar-queue-content::-webkit-scrollbar-thumb {
  background: var(--neon-green);
  border-radius: 4px;
}

.sidebar-queue-content::-webkit-scrollbar-thumb:hover {
  background: var(--neon-green-dark);
}

/* Video Item Components */
.video-item {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--bg-tertiary);
  border: 1px solid transparent;
  position: relative;
}

.video-item:hover {
  background: var(--border-primary);
}

.video-item-current {
  background: rgba(0, 255, 0, 0.2);
  border-color: var(--neon-green);
}

.video-item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.video-thumbnail {
  width: 48px;
  height: 36px;
  border-radius: var(--radius-sm);
  object-fit: cover;
  flex-shrink: 0;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
}

.video-author {
  font-size: 12px;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
}

.video-duration {
  font-size: 12px;
  color: var(--text-secondary);
  flex-shrink: 0;
}

.current-video-indicator {
  position: absolute;
  top: 50%;
  left: 4px;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: var(--neon-green);
  border-radius: 2px;
}

/* Playlist Item Components */
.playlist-item {
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.playlist-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.playlist-info {
  flex: 1;
  min-width: 0;
}

.playlist-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.playlist-meta {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

.playlist-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.playlist-action-button {
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.playlist-action-icon {
  width: 14px;
  height: 14px;
}

.playlist-delete-button:hover {
  color: var(--danger);
}

/* Mix Item Components */
.mix-item {
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.mix-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.mix-icon {
  flex-shrink: 0;
}

.mix-icon-svg {
  width: 20px;
  height: 20px;
  color: var(--neon-green);
}

.mix-info {
  flex: 1;
  min-width: 0;
}

.mix-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mix-meta {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

.mix-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.mix-action-button {
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.mix-action-icon {
  width: 14px;
  height: 14px;
}

.mix-delete-button:hover {
  color: var(--danger);
}

/* Help Modal Components */
.help-modal {
  width: 100%;
  max-width: 400px;
}

.help-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.help-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.help-close-button {
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.help-close-icon {
  width: 20px;
  height: 20px;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.help-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.help-section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: var(--neon-green);
}

.help-shortcut {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.help-action {
  color: var(--text-primary);
}

.help-keys {
  color: var(--neon-green);
  font-weight: 500;
}

.help-divider {
  border-top: 1px solid var(--border-secondary);
  margin: var(--spacing-md) 0;
}

/* Quit Confirmation Components */
.quit-modal {
  text-align: center;
  width: 100%;
  max-width: 400px;
}

.quit-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-md);
}

.quit-icon {
  width: 32px;
  height: 32px;
  color: var(--neon-green);
}

.quit-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.quit-message {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-size: 14px;
}

.quit-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

.quit-cancel-button,
.quit-confirm-button {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 14px;
  font-weight: 500;
}

/* App Layout */
.app-container {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  display: flex;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Glassmorphism effect */
.glass {
  background: rgba(26, 26, 26, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes lightning {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out forwards;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar-container {
    width: 280px;
  }

  .header-add-playlist .add-playlist-form {
    flex-direction: column;
  }

  .controls-main {
    gap: var(--spacing-md);
  }

  .control-icon,
  .control-icon-primary {
    width: 20px;
    height: 20px;
  }

  .volume-control {
    display: none;
  }
}

@media (max-width: 480px) {
  .sidebar-container {
    width: 100%;
    position: absolute;
    z-index: 10;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar-container.open {
    transform: translateX(0);
  }

  .main-content {
    width: 100%;
  }
}