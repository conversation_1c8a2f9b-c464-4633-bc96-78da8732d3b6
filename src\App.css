/* Tailwind CSS temporarily disabled for initial setup */

/* Custom Lightning Shuffler Styles */
:root {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  background: #0a0a0a;
  color: #ffffff;
  overflow: hidden;
}

/* Glassmorphism effect */
.glass {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neon glow effects */
.neon-glow {
  box-shadow: 0 0 5px #00ff00, 0 0 10px #00ff00, 0 0 15px #00ff00;
}

.neon-glow-strong {
  box-shadow: 0 0 10px #00ff00, 0 0 20px #00ff00, 0 0 30px #00ff00, 0 0 40px #00ff00;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(42, 42, 42, 0.5);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #00ff00;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #00cc00;
}

/* Video player gradient overlay */
.video-gradient {
  background: linear-gradient(45deg,
      rgba(0, 255, 0, 0.1) 0%,
      rgba(0, 255, 0, 0.05) 50%,
      rgba(0, 255, 0, 0.1) 100%);
}

/* Lightning bolt animation */
@keyframes lightning {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }
}

/* Spin animation for loading */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Fade in animation */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from left */
@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Pulse animation for buttons */
@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

/* Glow animation */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px #00ff00;
  }

  50% {
    box-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00;
  }

  100% {
    box-shadow: 0 0 5px #00ff00;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.pulse-on-hover:hover {
  animation: pulse 0.3s ease-in-out;
}

.glow-effect {
  animation: glow 2s ease-in-out infinite;
}

.lightning-pulse {
  animation: lightning 2s ease-in-out infinite;
}

/* Shuffle animation for cards */
.shuffle-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.shuffle-card.shuffling {
  animation: shuffle 0.6s ease-in-out;
}

/* Button hover effects */
.btn-neon {
  background: linear-gradient(45deg, #00ff00, #00cc00);
  border: none;
  color: #000;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-neon:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 0, 0.4);
}

.btn-neon:active {
  transform: translateY(0);
}

/* Search bar focus effect */
.search-focus {
  border-color: #00ff00;
  box-shadow: 0 0 0 3px rgba(0, 255, 0, 0.1);
}