import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { windowApi } from './utils/tauri';
import { filterVideos } from './utils/youtube';
import {
  useAppData,
  useMediaPlayer,
  useKeyboardShortcuts,
  useTauriEvents
} from './hooks';
import {
  Header,
  Sidebar,
  VideoPlayer,
  Controls,
  HelpModal,
  QuitConfirmation,
  Notification,
  LoadingSpinner
} from './components';
import './App.css';

/**
 * Main Lightning Shuffler App Component
 * Refactored for better organization and maintainability
 */
function App() {
  // Local UI state
  const [currentVideo, setCurrentVideo] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [playlistUrl, setPlaylistUrl] = useState('');
  const [showHelp, setShowHelp] = useState(false);
  const [showQuitConfirmation, setShowQuitConfirmation] = useState(false);
  const [loopCount, setLoopCount] = useState(0);

  // Custom hooks for data and functionality
  const {
    appData,
    isLoading,
    notification,
    addPlaylist,
    removePlaylist,
    refreshPlaylist,
    setQueueFromPlaylist,
    setQueueFromMix,
    setCurrentIndex,
    shuffleQueue
  } = useAppData();

  // Derived state
  const currentQueue = appData?.current_queue || [];
  const currentIndex = appData?.current_index || 0;
  const filteredVideos = filterVideos(currentQueue, searchQuery);

  // Set current video when app data changes
  useEffect(() => {
    if (appData && currentQueue.length > 0 && currentIndex < currentQueue.length) {
      setCurrentVideo(currentQueue[currentIndex]);
    } else {
      setCurrentVideo(null);
    }
  }, [appData, currentQueue, currentIndex]);

  // Set loop count from app data
  useEffect(() => {
    if (appData) {
      setLoopCount(appData.loop_count);
    }
  }, [appData]);

  // Media player hook
  const {
    isPlaying,
    volume,
    togglePlayPause,
    playNext,
    playPrevious,
    seekForward,
    seekBackward,
    toggleMute,
    setPlayerVolume,
    handleLoopClick,
    onPlayerReady,
    onPlayerStateChange
  } = useMediaPlayer({
    currentVideo,
    queue: currentQueue,
    currentIndex,
    loopCount,
    onVideoChange: (index) => setCurrentIndex(index),
    onLoopCountChange: setLoopCount
  });

  // Event handlers
  const handleAddPlaylist = async () => {
    const success = await addPlaylist(playlistUrl);
    if (success) {
      setPlaylistUrl('');
    }
  };

  const handlePlayPlaylist = async (playlist) => {
    await setQueueFromPlaylist(playlist, appData?.shuffle_enabled || false);
  };

  const handlePlayMix = async (mix) => {
    await setQueueFromMix(mix, appData?.shuffle_enabled || false);
  };

  const handleVideoClick = async (video) => {
    const videoIndex = currentQueue.findIndex(v => v.id === video.id);
    if (videoIndex !== -1) {
      await setCurrentIndex(videoIndex);
    }
  };

  const handleQuitApp = async () => {
    try {
      await windowApi.exit();
    } catch (error) {
      console.error('Failed to quit app:', error);
      window.close();
    }
  };

  // Setup keyboard shortcuts
  useKeyboardShortcuts({
    onPlayPause: togglePlayPause,
    onSeekForward: seekForward,
    onSeekBackward: seekBackward,
    onMute: toggleMute,
    onShowHelp: () => setShowHelp(true)
  });

  // Setup Tauri event listeners
  useTauriEvents({
    onPlayPause: togglePlayPause,
    onNext: playNext,
    onPrevious: playPrevious,
    onShowQuitConfirmation: () => setShowQuitConfirmation(true)
  });

  // Loading screen
  if (!appData) {
    return (
      <div className="app-container">
        <LoadingSpinner
          size="lg"
          text="Loading Lightning Shuffler..."
          variant="lightning"
        />
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: '#0a0a0a',
      color: 'white',
      display: 'flex',
      fontFamily: 'Inter, Segoe UI, Tahoma, Geneva, Verdana, sans-serif'
    }}>
      {/* Sidebar */}
      <div style={{
        width: '320px',
        background: '#1a1a1a',
        borderRight: '1px solid #374151',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Search Bar */}
        <div style={{ padding: '16px', borderBottom: '1px solid #374151' }}>
          <div style={{ position: 'relative' }}>
            <Search style={{
              position: 'absolute',
              left: '12px',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#9CA3AF',
              width: '16px',
              height: '16px'
            }} />
            <input
              type="text"
              placeholder="Search videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                width: 'calc(100% - 8px)',
                paddingLeft: '40px',
                paddingRight: '16px',
                paddingTop: '8px',
                paddingBottom: '8px',
                background: '#2a2a2a',
                border: '1px solid #4B5563',
                borderRadius: '8px',
                color: 'white',
                outline: 'none',
                boxSizing: 'border-box'
              }}
            />
          </div>
        </div>

        {/* Playlists Section */}
        <div style={{ padding: '16px', borderBottom: '1px solid #374151' }}>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '600',
            marginBottom: '12px',
            display: 'flex',
            alignItems: 'center'
          }}>
            Playlists
          </h3>

          {Object.values(appData.playlists).length === 0 ? (
            <p style={{ fontSize: '14px', color: '#9CA3AF', textAlign: 'center', padding: '16px 0' }}>
              No playlists added yet
            </p>
          ) : (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {Object.values(appData.playlists).map((playlist, index) => (
                <div
                  key={playlist.id}
                  className="slide-in-left"
                  style={{
                    padding: '8px',
                    borderRadius: '6px',
                    background: '#2a2a2a',
                    cursor: 'pointer',
                    transition: 'background 0.2s',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    animationDelay: `${index * 0.1}s`
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.background = '#374151'}
                  onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
                  onClick={() => playPlaylist(playlist)}
                >
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <p style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      margin: 0,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {playlist.title}
                    </p>
                    <p style={{
                      fontSize: '12px',
                      color: '#9CA3AF',
                      margin: 0
                    }}>
                      {playlist.video_count} videos
                    </p>
                  </div>
                  <div style={{ display: 'flex', gap: '4px' }}>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Refresh playlist functionality would go here
                      }}
                      style={{
                        padding: '4px',
                        background: 'transparent',
                        border: 'none',
                        color: '#9CA3AF',
                        cursor: 'pointer',
                        borderRadius: '4px'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.color = '#00ff00'}
                      onMouseLeave={(e) => e.currentTarget.style.color = '#9CA3AF'}
                    >
                      <RefreshCw style={{ width: '14px', height: '14px' }} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removePlaylist(playlist.id);
                      }}
                      style={{
                        padding: '4px',
                        background: 'transparent',
                        border: 'none',
                        color: '#9CA3AF',
                        cursor: 'pointer',
                        borderRadius: '4px'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.color = '#ff4444'}
                      onMouseLeave={(e) => e.currentTarget.style.color = '#9CA3AF'}
                    >
                      <Trash2 style={{ width: '14px', height: '14px' }} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Queue */}
        <div style={{ flex: 1, overflowY: 'auto' }}>
          <div style={{ padding: '16px' }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              marginBottom: '16px',
              display: 'flex',
              alignItems: 'center'
            }}>
              <Zap style={{
                width: '20px',
                height: '20px',
                marginRight: '8px',
                color: '#00ff00'
              }} />
              Current Queue
            </h3>

            {filteredVideos.map((video, index) => (
              <div
                key={video.id}
                className="fade-in"
                style={{
                  padding: '12px',
                  borderRadius: '8px',
                  marginBottom: '8px',
                  cursor: 'pointer',
                  background: currentVideo?.id === video.id
                    ? 'rgba(0, 255, 0, 0.2)'
                    : '#2a2a2a',
                  border: currentVideo?.id === video.id
                    ? '1px solid #00ff00'
                    : '1px solid transparent',
                  transition: 'all 0.2s',
                  animationDelay: `${index * 0.05}s`
                }}
                onClick={() => setCurrentVideo(video)}
                onMouseEnter={(e) => {
                  if (currentVideo?.id !== video.id) {
                    e.currentTarget.style.background = '#374151';
                  }
                }}
                onMouseLeave={(e) => {
                  if (currentVideo?.id !== video.id) {
                    e.currentTarget.style.background = '#2a2a2a';
                  }
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    style={{
                      width: '48px',
                      height: '36px',
                      borderRadius: '4px',
                      objectFit: 'cover'
                    }}
                  />
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <p style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      margin: 0
                    }}>
                      {video.title}
                    </p>
                    <p style={{
                      fontSize: '12px',
                      color: '#9CA3AF',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      margin: 0
                    }}>
                      {video.author}
                    </p>
                  </div>
                  <span style={{ fontSize: '12px', color: '#9CA3AF' }}>
                    {video.duration}
                  </span>
                </div>
              </div>
            ))}

            {filteredVideos.length === 0 && searchQuery && (
              <div style={{ textAlign: 'center', padding: '32px 0', color: '#9CA3AF' }}>
                <p>No results found</p>
                <p style={{ fontSize: '14px', marginTop: '4px' }}>
                  Check your spelling or try different keywords
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <div style={{ padding: '24px', borderBottom: '1px solid #374151' }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <img
                src="/Lightning_Bolt.png"
                alt="Lightning Shuffler"
                style={{ width: '32px', height: '32px' }}
              />
              <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
                Lightning Shuffler
              </h1>
            </div>

            <button
              onClick={() => setShowHelp(true)}
              style={{
                padding: '8px',
                borderRadius: '8px',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <HelpCircle style={{ width: '20px', height: '20px' }} />
            </button>
          </div>

          {/* Add Playlist */}
          <div style={{ marginTop: '16px', display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <div style={{ display: 'flex', gap: '8px' }}>
              <input
                type="text"
                placeholder="Enter YouTube playlist URL..."
                value={playlistUrl}
                onChange={(e) => setPlaylistUrl(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    addPlaylist();
                  }
                }}
                style={{
                  flex: 1,
                  padding: '8px 16px',
                  background: '#2a2a2a',
                  border: '1px solid #4B5563',
                  borderRadius: '8px',
                  color: 'white',
                  outline: 'none'
                }}
              />
              <button
                onClick={addPlaylist}
                disabled={isLoading}
                style={{
                  padding: '8px 24px',
                  background: 'linear-gradient(45deg, #00ff00, #00cc00)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'black',
                  fontWeight: '600',
                  cursor: isLoading ? 'not-allowed' : 'pointer',
                  opacity: isLoading ? 0.5 : 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'transform 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!isLoading) e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  if (!isLoading) e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                {isLoading ? (
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid black',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                ) : (
                  <Plus style={{ width: '16px', height: '16px' }} />
                )}
                <span>Add Playlist</span>
              </button>
            </div>


          </div>
        </div>

        {/* Video Player */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0
        }}>
          <div style={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '24px',
            paddingBottom: '12px'
          }}>
            {currentVideo ? (
              <div style={{ width: '100%', maxWidth: '900px' }}>
                <div style={{
                  position: 'relative',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  background: 'linear-gradient(45deg, rgba(0, 255, 0, 0.1) 0%, rgba(0, 255, 0, 0.05) 50%, rgba(0, 255, 0, 0.1) 100%)',
                  padding: '4px'
                }}>
                  <div style={{ borderRadius: '8px', overflow: 'hidden' }}>
                    <YouTube
                      videoId={currentVideo.id}
                      opts={{
                        width: '100%',
                        height: '400',
                        playerVars: {
                          autoplay: 1,
                          controls: 0,
                          modestbranding: 1,
                          rel: 0,
                        },
                      }}
                      onReady={onPlayerReady}
                      onStateChange={onPlayerStateChange}
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div style={{ textAlign: 'center' }}>
                <Zap style={{
                  width: '64px',
                  height: '64px',
                  margin: '0 auto 16px',
                  color: '#00ff00',
                  animation: 'lightning 2s ease-in-out infinite'
                }} />
                <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '8px' }}>
                  Welcome to Lightning Shuffler
                </h2>
                <p style={{ color: '#9CA3AF' }}>Add a playlist to get started</p>
              </div>
            )}
          </div>

          {/* Video Info - Fixed position above controls */}
          {currentVideo && (
            <div style={{
              textAlign: 'center',
              padding: '0 24px 12px',
              borderBottom: '1px solid #374151'
            }}>
              <h2 style={{
                fontSize: '18px',
                fontWeight: '600',
                margin: 0,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {currentVideo.title}
              </h2>
              <p style={{
                color: '#9CA3AF',
                margin: '4px 0 0 0',
                fontSize: '14px'
              }}>
                {currentVideo.author}
              </p>
            </div>
          )}
        </div>

        {/* Controls */}
        <div style={{ padding: '24px', borderTop: '1px solid #374151' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '24px'
          }}>
            <button
              onClick={playPrevious}
              style={{
                padding: '12px',
                borderRadius: '50%',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <SkipBack style={{ width: '24px', height: '24px' }} />
            </button>

            <button
              onClick={togglePlayPause}
              style={{
                padding: '16px',
                borderRadius: '50%',
                background: 'linear-gradient(45deg, #00ff00, #00cc00)',
                border: 'none',
                color: 'black',
                cursor: 'pointer',
                transition: 'transform 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              {isPlaying ?
                <Pause style={{ width: '32px', height: '32px' }} /> :
                <Play style={{ width: '32px', height: '32px' }} />
              }
            </button>

            <button
              onClick={playNext}
              style={{
                padding: '12px',
                borderRadius: '50%',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <SkipForward style={{ width: '24px', height: '24px' }} />
            </button>

            <button
              onClick={shuffleQueue}
              style={{
                padding: '12px',
                borderRadius: '50%',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <Shuffle style={{ width: '24px', height: '24px' }} />
            </button>

            <button
              onClick={handleLoopClick}
              onContextMenu={(e) => {
                e.preventDefault();
                handleLoopClick(e);
              }}
              style={{
                padding: '12px',
                borderRadius: '50%',
                background: '#2a2a2a',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                transition: 'background 0.2s',
                position: 'relative'
              }}
              onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
              onMouseLeave={(e) => e.currentTarget.style.background = '#2a2a2a'}
            >
              <Repeat style={{ width: '24px', height: '24px' }} />
              {loopCount > 0 && (
                <span style={{
                  position: 'absolute',
                  top: '-4px',
                  right: '-4px',
                  background: '#00ff00',
                  color: 'black',
                  fontSize: '12px',
                  borderRadius: '50%',
                  width: '20px',
                  height: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontWeight: 'bold'
                }}>
                  {loopCount}
                </span>
              )}
            </button>

            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Volume2 style={{ width: '20px', height: '20px' }} />
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => {
                  const newVolume = parseInt(e.target.value);
                  setVolume(newVolume);
                  if (playerRef.current) {
                    playerRef.current.setVolume(newVolume);
                  }
                }}
                style={{
                  width: '80px',
                  accentColor: '#00ff00'
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Help Modal */}
      {showHelp && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 50
          }}
          onClick={() => setShowHelp(false)}
        >
          <div
            style={{
              background: 'rgba(26, 26, 26, 0.9)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              padding: '24px',
              borderRadius: '12px',
              maxWidth: '400px',
              width: '100%',
              margin: '16px'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '16px'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', margin: 0 }}>
                Keyboard Shortcuts
              </h3>
              <button
                onClick={() => setShowHelp(false)}
                style={{
                  padding: '4px',
                  borderRadius: '4px',
                  background: 'transparent',
                  border: 'none',
                  color: 'white',
                  cursor: 'pointer',
                  transition: 'background 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.background = '#4B5563'}
                onMouseLeave={(e) => e.currentTarget.style.background = 'transparent'}
              >
                <X style={{ width: '20px', height: '20px' }} />
              </button>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', fontSize: '14px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Play/Pause</span>
                <span style={{ color: '#00ff00' }}>Space or K</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Seek Forward 10s</span>
                <span style={{ color: '#00ff00' }}>Right Arrow</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Seek Backward 10s</span>
                <span style={{ color: '#00ff00' }}>Left Arrow</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Mute/Unmute</span>
                <span style={{ color: '#00ff00' }}>M</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>Show Help</span>
                <span style={{ color: '#00ff00' }}>H</span>
              </div>
              <div style={{
                borderTop: '1px solid #4B5563',
                paddingTop: '12px',
                marginTop: '12px'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>Loop Toggle</span>
                  <span style={{ color: '#00ff00' }}>Click Loop Button</span>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>Loop Count +1</span>
                  <span style={{ color: '#00ff00' }}>Shift+Click or Right-Click</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quit Confirmation Dialog */}
      {showQuitConfirmation && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 100
          }}
          onClick={() => setShowQuitConfirmation(false)}
        >
          <div
            style={{
              background: 'rgba(26, 26, 26, 0.95)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              padding: '32px',
              borderRadius: '12px',
              maxWidth: '400px',
              width: '100%',
              margin: '16px',
              textAlign: 'center'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '16px'
            }}>
              <Zap style={{
                width: '32px',
                height: '32px',
                color: '#00ff00',
                marginRight: '12px'
              }} />
              <h3 style={{ fontSize: '20px', fontWeight: '600', margin: 0 }}>
                Quit Lightning Shuffler?
              </h3>
            </div>

            <p style={{
              color: '#9CA3AF',
              marginBottom: '24px',
              fontSize: '14px'
            }}>
              Are you sure you want to quit Lightning Shuffler?
            </p>

            <div style={{
              display: 'flex',
              gap: '12px',
              justifyContent: 'center'
            }}>
              <button
                onClick={() => setShowQuitConfirmation(false)}
                style={{
                  padding: '12px 24px',
                  borderRadius: '8px',
                  background: '#2a2a2a',
                  border: '1px solid #4B5563',
                  color: 'white',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = '#4B5563';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = '#2a2a2a';
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleQuitApp}
                style={{
                  padding: '12px 24px',
                  borderRadius: '8px',
                  background: 'linear-gradient(45deg, #ff4444, #cc0000)',
                  border: 'none',
                  color: 'white',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 68, 68, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                Quit Lightning Shuffler
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Notification */}
      {notification && (
        <div
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            background: notification.type === 'success' ? '#00ff00' : '#ff4444',
            color: notification.type === 'success' ? '#000' : '#fff',
            fontWeight: '600',
            zIndex: 1000,
            animation: 'fadeIn 0.3s ease-out'
          }}
        >
          {notification.message}
        </div>
      )}
    </div>
  );
}

export default App;