import React from 'react';
import { motion } from 'framer-motion';
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Shuffle,
  Repeat,
  Volume2
} from 'lucide-react';
import { ControlsProps } from '../types';
import { Button } from './ui';

/**
 * Media controls component with loop counter and volume
 */
export const Controls: React.FC<ControlsProps> = ({
  isPlaying,
  loopCount,
  volume,
  onPlayPause,
  onPrevious,
  onNext,
  onShuffle,
  onLoopClick,
  onVolumeChange
}) => {
  return (
    <div className="controls-container">
      <div className="controls-main">
        {/* Previous Button */}
        <Button
          variant="ghost"
          size="lg"
          onClick={onPrevious}
          className="control-button"
        >
          <SkipBack className="control-icon" />
        </Button>

        {/* Play/Pause Button */}
        <Button
          variant="primary"
          size="lg"
          onClick={onPlayPause}
          className="control-button-primary"
        >
          {isPlaying ? (
            <Pause className="control-icon-primary" />
          ) : (
            <Play className="control-icon-primary" />
          )}
        </Button>

        {/* Next Button */}
        <Button
          variant="ghost"
          size="lg"
          onClick={onNext}
          className="control-button"
        >
          <SkipForward className="control-icon" />
        </Button>

        {/* Shuffle Button */}
        <Button
          variant="ghost"
          size="lg"
          onClick={onShuffle}
          className="control-button"
        >
          <Shuffle className="control-icon" />
        </Button>

        {/* Loop Button with Counter */}
        <Button
          variant="ghost"
          size="lg"
          onClick={onLoopClick}
          onContextMenu={(e) => {
            e.preventDefault();
            onLoopClick(e);
          }}
          className="control-button loop-button"
        >
          <Repeat className="control-icon" />
          {loopCount > 0 && (
            <motion.span
              className="loop-counter"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              {loopCount}
            </motion.span>
          )}
        </Button>

        {/* Volume Control */}
        <div className="volume-control">
          <Volume2 className="volume-icon" />
          <input
            type="range"
            min="0"
            max="100"
            value={volume}
            onChange={(e) => onVolumeChange(parseInt(e.target.value))}
            className="volume-slider"
          />
        </div>
      </div>
    </div>
  );
};
