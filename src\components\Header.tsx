import React from 'react';
import { motion } from 'framer-motion';
import { Plus, HelpCircle } from 'lucide-react';
import { HeaderProps } from '../types';
import { Button, Input } from './ui';

/**
 * App header with branding and playlist addition
 */
export const Header: React.FC<HeaderProps> = ({
  playlistUrl,
  isLoading,
  onPlaylistUrlChange,
  onAddPlaylist,
  onShowHelp
}) => {
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      onAddPlaylist();
    }
  };

  return (
    <div className="header-container">
      <div className="header-top">
        <motion.div
          className="header-brand"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <img
            src="/Lightning_Bolt.png"
            alt="Lightning Shuffler"
            className="header-logo"
          />
          <h1 className="header-title"><PERSON> Shuffler</h1>
        </motion.div>

        <Button
          variant="ghost"
          size="md"
          onClick={onShowHelp}
          className="header-help-button"
        >
          <HelpCircle className="header-help-icon" />
        </Button>
      </div>

      <motion.div
        className="header-add-playlist"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="add-playlist-form">
          <Input
            type="text"
            placeholder="Enter YouTube playlist URL..."
            value={playlistUrl}
            onChange={(e) => onPlaylistUrlChange(e.target.value)}
            onKeyPress={handleKeyPress}
            className="add-playlist-input"
          />
          <Button
            variant="primary"
            onClick={onAddPlaylist}
            isLoading={isLoading}
            disabled={isLoading}
            className="add-playlist-button"
          >
            {!isLoading && <Plus className="add-playlist-icon" />}
            Add Playlist
          </Button>
        </div>
      </motion.div>
    </div>
  );
};
