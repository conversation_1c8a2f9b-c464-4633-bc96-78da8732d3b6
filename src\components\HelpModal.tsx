import React from 'react';
import { X } from 'lucide-react';
import { HelpModalProps } from '../types';
import { Modal, Button } from './ui';

/**
 * Help modal showing keyboard shortcuts
 */
export const HelpModal: React.FC<HelpModalProps> = ({ isOpen, onClose }) => {
  const shortcuts = [
    { action: 'Play/Pause', keys: 'Space or K' },
    { action: 'Seek Forward 10s', keys: 'Right Arrow' },
    { action: 'Seek Backward 10s', keys: 'Left Arrow' },
    { action: 'Mute/Unmute', keys: 'M' },
    { action: 'Show Help', keys: 'H' },
  ];

  const loopShortcuts = [
    { action: 'Loop Toggle', keys: 'Click Loop Button' },
    { action: 'Loop Count +1', keys: 'Shift+Click or Right-Click' },
  ];

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="help-modal">
        <div className="help-header">
          <h3 className="help-title">Keyboard Shortcuts</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="help-close-button"
          >
            <X className="help-close-icon" />
          </Button>
        </div>

        <div className="help-content">
          <div className="help-section">
            {shortcuts.map((shortcut, index) => (
              <div key={index} className="help-shortcut">
                <span className="help-action">{shortcut.action}</span>
                <span className="help-keys">{shortcut.keys}</span>
              </div>
            ))}
          </div>

          <div className="help-divider" />

          <div className="help-section">
            <h4 className="help-section-title">Loop Controls</h4>
            {loopShortcuts.map((shortcut, index) => (
              <div key={index} className="help-shortcut">
                <span className="help-action">{shortcut.action}</span>
                <span className="help-keys">{shortcut.keys}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Modal>
  );
};
