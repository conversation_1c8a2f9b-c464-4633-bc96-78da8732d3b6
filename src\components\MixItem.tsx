import React from 'react';
import { motion } from 'framer-motion';
import { Edit, Trash2, Layers } from 'lucide-react';
import { MixItemProps } from '../types';
import { Button } from './ui';
import { combinePlaylistsForMix } from '../utils/youtube';

/**
 * Individual mix item component
 */
export const MixItem: React.FC<MixItemProps> = ({
  mix,
  playlists,
  onPlay,
  onEdit,
  onDelete,
  index
}) => {
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Are you sure you want to delete this mix?')) {
      onDelete(mix.id);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(mix.id);
  };

  // Calculate total videos in mix
  const totalVideos = combinePlaylistsForMix(mix.playlist_ids, playlists).length;
  const validPlaylists = mix.playlist_ids.filter(id => playlists[id]).length;

  return (
    <motion.div
      className="mix-item"
      onClick={() => onPlay(mix)}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      whileHover={{ scale: 1.02, backgroundColor: 'rgba(55, 65, 81, 0.8)' }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="mix-content">
        <div className="mix-icon">
          <Layers className="mix-icon-svg" />
        </div>
        
        <div className="mix-info">
          <p className="mix-title">
            {mix.name}
          </p>
          <p className="mix-meta">
            {validPlaylists} playlists • {totalVideos} videos
          </p>
        </div>
        
        <div className="mix-actions">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            className="mix-action-button"
          >
            <Edit className="mix-action-icon" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            className="mix-action-button mix-delete-button"
          >
            <Trash2 className="mix-action-icon" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};
