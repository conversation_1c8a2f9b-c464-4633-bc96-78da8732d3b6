import React from 'react';
import { motion } from 'framer-motion';
import { RefreshCw, Trash2 } from 'lucide-react';
import { PlaylistItemProps } from '../types';
import { Button } from './ui';

/**
 * Individual playlist item component
 */
export const PlaylistItem: React.FC<PlaylistItemProps> = ({
  playlist,
  onPlay,
  onRefresh,
  onDelete,
  index
}) => {
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Are you sure you want to delete this playlist?')) {
      onDelete(playlist.id);
    }
  };

  const handleRefresh = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRefresh(playlist.id);
  };

  return (
    <motion.div
      className="playlist-item"
      onClick={() => onPlay(playlist)}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      whileHover={{ scale: 1.02, backgroundColor: 'rgba(55, 65, 81, 0.8)' }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="playlist-content">
        <div className="playlist-info">
          <p className="playlist-title">
            {playlist.title}
          </p>
          <p className="playlist-meta">
            {playlist.video_count} videos
          </p>
        </div>
        
        <div className="playlist-actions">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            className="playlist-action-button"
          >
            <RefreshCw className="playlist-action-icon" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            className="playlist-action-button playlist-delete-button"
          >
            <Trash2 className="playlist-action-icon" />
          </Button>
        </div>
      </div>
    </motion.div>
  );
};
