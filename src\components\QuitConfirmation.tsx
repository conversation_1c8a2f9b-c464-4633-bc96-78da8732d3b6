import React from 'react';
import { Zap } from 'lucide-react';
import { QuitConfirmationProps } from '../types';
import { Modal, Button } from './ui';

/**
 * Quit confirmation dialog
 */
export const QuitConfirmation: React.FC<QuitConfirmationProps> = ({
  isOpen,
  onConfirm,
  onCancel
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onCancel}>
      <div className="quit-modal">
        <div className="quit-header">
          <Zap className="quit-icon" />
          <h3 className="quit-title">Quit Lightning Shuffler?</h3>
        </div>

        <p className="quit-message">
          Are you sure you want to quit Lightning Shuffler?
        </p>

        <div className="quit-actions">
          <Button
            variant="secondary"
            onClick={onCancel}
            className="quit-cancel-button"
          >
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={onConfirm}
            className="quit-confirm-button"
          >
            Quit Lightning Shuffler
          </Button>
        </div>
      </div>
    </Modal>
  );
};
