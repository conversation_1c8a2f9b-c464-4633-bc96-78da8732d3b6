import React from 'react';
import { Search } from 'lucide-react';
import { Input } from './ui';
import { SearchBarProps } from '../types';

/**
 * Search bar component for filtering videos
 */
export const SearchBar: React.FC<SearchBarProps> = ({
  searchQuery,
  onSearchChange
}) => {
  return (
    <div className="search-bar-container">
      <Input
        type="text"
        placeholder="Search videos..."
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        icon={<Search className="search-icon" />}
        iconPosition="left"
        className="search-input"
      />
    </div>
  );
};
