import React from 'react';
import { motion } from 'framer-motion';
import { Zap } from 'lucide-react';
import { SidebarProps } from '../types';
import { SearchBar } from './SearchBar';
import { PlaylistItem } from './PlaylistItem';
import { MixItem } from './MixItem';
import { VideoItem } from './VideoItem';

/**
 * Sidebar component containing search, playlists, mixes, and queue
 */
export const Sidebar: React.FC<SidebarProps> = ({
  searchQuery,
  onSearchChange,
  playlists,
  mixes,
  filteredVideos,
  currentVideo,
  onPlayPlaylist,
  onPlayMix,
  onRefreshPlaylist,
  onDeletePlaylist,
  onDeleteMix,
  onVideoClick
}) => {
  const playlistArray = Object.values(playlists);
  const mixArray = Object.values(mixes);

  return (
    <div className="sidebar-container">
      {/* Search Bar */}
      <div className="sidebar-search">
        <SearchBar
          searchQuery={searchQuery}
          onSearchChange={onSearchChange}
        />
      </div>

      {/* Playlists Section */}
      <div className="sidebar-section">
        <h3 className="sidebar-section-title">Playlists</h3>
        
        {playlistArray.length === 0 ? (
          <p className="sidebar-empty-message">
            No playlists added yet
          </p>
        ) : (
          <div className="sidebar-items">
            {playlistArray.map((playlist, index) => (
              <PlaylistItem
                key={playlist.id}
                playlist={playlist}
                onPlay={onPlayPlaylist}
                onRefresh={onRefreshPlaylist}
                onDelete={onDeletePlaylist}
                index={index}
              />
            ))}
          </div>
        )}
      </div>

      {/* Mixes Section */}
      {mixArray.length > 0 && (
        <div className="sidebar-section">
          <h3 className="sidebar-section-title">Mixes</h3>
          <div className="sidebar-items">
            {mixArray.map((mix, index) => (
              <MixItem
                key={mix.id}
                mix={mix}
                playlists={playlists}
                onPlay={onPlayMix}
                onEdit={() => {}} // TODO: Implement mix editing
                onDelete={onDeleteMix}
                index={index}
              />
            ))}
          </div>
        </div>
      )}

      {/* Current Queue */}
      <div className="sidebar-section sidebar-queue">
        <motion.h3
          className="sidebar-section-title queue-title"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Zap className="queue-icon" />
          Current Queue
        </motion.h3>

        <div className="sidebar-queue-content">
          {filteredVideos.length === 0 && searchQuery ? (
            <div className="sidebar-no-results">
              <p>No results found</p>
              <p className="sidebar-no-results-hint">
                Check your spelling or try different keywords
              </p>
            </div>
          ) : (
            <div className="sidebar-items queue-items">
              {filteredVideos.map((video, index) => (
                <VideoItem
                  key={video.id}
                  video={video}
                  isCurrentVideo={currentVideo?.id === video.id}
                  onClick={onVideoClick}
                  index={index}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
