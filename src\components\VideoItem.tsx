import React from 'react';
import { motion } from 'framer-motion';
import { VideoItemProps } from '../types';

/**
 * Individual video item component for the queue
 */
export const VideoItem: React.FC<VideoItemProps> = ({
  video,
  isCurrentVideo,
  onClick,
  index
}) => {
  return (
    <motion.div
      className={`video-item ${isCurrentVideo ? 'video-item-current' : ''}`}
      onClick={() => onClick(video)}
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="video-item-content">
        <img
          src={video.thumbnail}
          alt={video.title}
          className="video-thumbnail"
          loading="lazy"
        />
        <div className="video-details">
          <p className="video-title">
            {video.title}
          </p>
          <p className="video-author">
            {video.author}
          </p>
        </div>
        <span className="video-duration">
          {video.duration}
        </span>
      </div>
      
      {isCurrentVideo && (
        <motion.div
          className="current-video-indicator"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 300 }}
        />
      )}
    </motion.div>
  );
};
