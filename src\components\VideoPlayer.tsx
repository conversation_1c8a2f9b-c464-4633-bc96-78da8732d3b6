import React from 'react';
import { motion } from 'framer-motion';
import YouTube from 'react-youtube';
import { Zap } from 'lucide-react';
import { VideoPlayerProps } from '../types';

/**
 * YouTube video player component with Lightning Shuffler styling
 */
export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  currentVideo,
  onReady,
  onStateChange
}) => {
  if (!currentVideo) {
    return (
      <div className="video-player-empty">
        <motion.div
          className="welcome-content"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            animate={{
              opacity: [1, 0.7, 1],
              scale: [1, 1.1, 1]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            <Zap className="welcome-icon" />
          </motion.div>
          <h2 className="welcome-title">
            Welcome to Lightning Shuffler
          </h2>
          <p className="welcome-subtitle">Add a playlist to get started</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="video-player-container">
      <motion.div
        className="video-player-wrapper"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="video-gradient-border">
          <div className="video-inner">
            <YouTube
              videoId={currentVideo.id}
              opts={{
                width: '100%',
                height: '400',
                playerVars: {
                  autoplay: 1,
                  controls: 0,
                  modestbranding: 1,
                  rel: 0,
                },
              }}
              onReady={onReady}
              onStateChange={onStateChange}
              className="youtube-player"
            />
          </div>
        </div>
      </motion.div>
      
      {/* Video Info */}
      <motion.div
        className="video-info"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: 0.2 }}
      >
        <h2 className="video-title">
          {currentVideo.title}
        </h2>
        <p className="video-author">
          {currentVideo.author}
        </p>
      </motion.div>
    </div>
  );
};
