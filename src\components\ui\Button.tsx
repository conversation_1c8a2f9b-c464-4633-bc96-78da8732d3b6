import React from 'react';
import { motion } from 'framer-motion';
import { classNames } from '../../utils';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  children: React.ReactNode;
}

/**
 * Reusable button component with animations and variants
 */
export const Button: React.FC<ButtonProps> = ({
  variant = 'secondary',
  size = 'md',
  isLoading = false,
  disabled,
  children,
  className = '',
  ...props
}) => {
  const baseClasses = 'btn-base';
  const variantClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
    danger: 'btn-danger',
    ghost: 'btn-ghost'
  };
  const sizeClasses = {
    sm: 'btn-sm',
    md: 'btn-md',
    lg: 'btn-lg'
  };

  const classes = classNames({
    [baseClasses]: true,
    [variantClasses[variant]]: true,
    [sizeClasses[size]]: true,
    'btn-loading': isLoading,
    'btn-disabled': disabled || isLoading,
    [className]: !!className
  });

  return (
    <motion.button
      className={classes}
      disabled={disabled || isLoading}
      whileHover={!disabled && !isLoading ? { scale: 1.02, y: -1 } : {}}
      whileTap={!disabled && !isLoading ? { scale: 0.98 } : {}}
      transition={{ duration: 0.2 }}
      {...props}
    >
      {isLoading && (
        <div className="loading-spinner" />
      )}
      <span className={isLoading ? 'loading-text' : ''}>{children}</span>
    </motion.button>
  );
};
