import React, { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { classNames } from '../../utils';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

/**
 * Reusable input component with animations and validation
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  icon,
  iconPosition = 'left',
  className = '',
  ...props
}, ref) => {
  const inputClasses = classNames({
    'input-base': true,
    'input-with-icon': !!icon,
    'input-with-icon-left': !!icon && iconPosition === 'left',
    'input-with-icon-right': !!icon && iconPosition === 'right',
    'input-error': !!error,
    [className]: !!className
  });

  return (
    <div className="input-container">
      {label && (
        <label className="input-label">
          {label}
        </label>
      )}
      <div className="input-wrapper">
        {icon && iconPosition === 'left' && (
          <div className="input-icon input-icon-left">
            {icon}
          </div>
        )}
        <motion.input
          ref={ref}
          className={inputClasses}
          whileFocus={{ scale: 1.01 }}
          transition={{ duration: 0.2 }}
          {...props}
        />
        {icon && iconPosition === 'right' && (
          <div className="input-icon input-icon-right">
            {icon}
          </div>
        )}
      </div>
      {error && (
        <motion.div
          className="input-error-message"
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -5 }}
        >
          {error}
        </motion.div>
      )}
    </div>
  );
});
