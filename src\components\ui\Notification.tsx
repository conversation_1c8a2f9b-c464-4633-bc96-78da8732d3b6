import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, XCircle } from 'lucide-react';
import { NotificationProps } from '../../types';

/**
 * Toast notification component
 */
export const Notification: React.FC<NotificationProps> = ({ notification }) => {
  if (!notification) return null;

  const Icon = notification.type === 'success' ? CheckCircle : XCircle;

  return (
    <AnimatePresence>
      <motion.div
        className={`notification notification-${notification.type}`}
        initial={{ opacity: 0, y: -50, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -50, scale: 0.9 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <Icon className="notification-icon" />
        <span className="notification-message">{notification.message}</span>
      </motion.div>
    </AnimatePresence>
  );
};
