import { useEffect } from 'react';
import { keyboardApi } from '../utils/tauri';

interface UseKeyboardShortcutsProps {
  onPlayPause: () => void;
  onSeekForward: () => void;
  onSeekBackward: () => void;
  onMute: () => void;
  onShowHelp: () => void;
}

/**
 * Custom hook for managing keyboard shortcuts
 */
export const useKeyboardShortcuts = ({
  onPlayPause,
  onSeekForward,
  onSeekBackward,
  onMute,
  onShowHelp
}: UseKeyboardShortcutsProps) => {
  useEffect(() => {
    const cleanup = keyboardApi.setup({
      onPlayPause,
      onSeekForward,
      onSeekBackward,
      onMute,
      onShowHelp
    });

    return cleanup;
  }, [onPlayPause, onSeekForward, onSeekBackward, onMute, onShowHelp]);
};
