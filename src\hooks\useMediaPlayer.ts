import { useState, useEffect, useCallback, useRef } from 'react';
import { Video, YouTubePlayer, YouTubePlayerEvent } from '../types';
import { mediaSessionApi, trayApi } from '../utils/tauri';
import { getNextVideoIndex, getPreviousVideoIndex } from '../utils/youtube';

interface UseMediaPlayerProps {
  currentVideo: Video | null;
  queue: Video[];
  currentIndex: number;
  loopCount: number;
  onVideoChange: (index: number) => void;
  onLoopCountChange: (count: number) => void;
}

/**
 * Custom hook for managing media player state and controls
 */
export const useMediaPlayer = ({
  currentVideo,
  queue,
  currentIndex,
  loopCount,
  onVideoChange,
  onLoopCountChange
}: UseMediaPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const playerRef = useRef<YouTubePlayer | null>(null);

  /**
   * Update tray tooltip with current video info
   */
  const updateTrayTooltip = useCallback(async () => {
    if (currentVideo) {
      const playIcon = isPlaying ? '▶️' : '⏸️';
      await trayApi.updateTooltip(`${playIcon} ${currentVideo.title}`);
    }
  }, [currentVideo, isPlaying]);

  /**
   * Play/pause toggle
   */
  const togglePlayPause = useCallback(() => {
    if (playerRef.current) {
      if (isPlaying) {
        playerRef.current.pauseVideo();
      } else {
        playerRef.current.playVideo();
      }
      setIsPlaying(!isPlaying);
    }
  }, [isPlaying]);

  /**
   * Play next video
   */
  const playNext = useCallback(() => {
    if (queue.length === 0) return;
    
    const nextIndex = getNextVideoIndex(currentIndex, queue.length);
    onVideoChange(nextIndex);
  }, [queue, currentIndex, onVideoChange]);

  /**
   * Play previous video
   */
  const playPrevious = useCallback(() => {
    if (queue.length === 0) return;
    
    const prevIndex = getPreviousVideoIndex(currentIndex, queue.length);
    onVideoChange(prevIndex);
  }, [queue, currentIndex, onVideoChange]);

  /**
   * Seek forward by 10 seconds
   */
  const seekForward = useCallback(() => {
    if (playerRef.current) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(currentTime + 10);
    }
  }, []);

  /**
   * Seek backward by 10 seconds
   */
  const seekBackward = useCallback(() => {
    if (playerRef.current) {
      const currentTime = playerRef.current.getCurrentTime();
      playerRef.current.seekTo(Math.max(0, currentTime - 10));
    }
  }, []);

  /**
   * Toggle mute
   */
  const toggleMute = useCallback(() => {
    if (playerRef.current) {
      if (playerRef.current.isMuted()) {
        playerRef.current.unMute();
        setIsMuted(false);
      } else {
        playerRef.current.mute();
        setIsMuted(true);
      }
    }
  }, []);

  /**
   * Set volume
   */
  const setPlayerVolume = useCallback((newVolume: number) => {
    setVolume(newVolume);
    if (playerRef.current) {
      playerRef.current.setVolume(newVolume);
    }
  }, []);

  /**
   * Handle loop button click
   */
  const handleLoopClick = useCallback((e: React.MouseEvent) => {
    if (e.shiftKey || e.button === 2) {
      // Shift+click or right-click: increment loop count
      onLoopCountChange(loopCount + 1);
    } else {
      // Regular click: toggle loop on/off
      onLoopCountChange(loopCount > 0 ? 0 : 1);
    }
  }, [loopCount, onLoopCountChange]);

  /**
   * YouTube player ready handler
   */
  const onPlayerReady = useCallback((event: any) => {
    playerRef.current = event.target;
    event.target.setVolume(volume);
  }, [volume]);

  /**
   * YouTube player state change handler
   */
  const onPlayerStateChange = useCallback((event: YouTubePlayerEvent) => {
    const playerState = event.data;
    // YouTube player states: -1 (unstarted), 0 (ended), 1 (playing), 2 (paused), 3 (buffering), 5 (cued)
    const playing = playerState === 1;
    setIsPlaying(playing);

    // Update media session playback state
    mediaSessionApi.updatePlaybackState(playing);

    if (playerState === 0) { // Video ended
      if (loopCount > 0) {
        // Loop current video
        onLoopCountChange(loopCount - 1);
        event.target.playVideo();
      } else {
        // Play next video
        playNext();
      }
    }
  }, [loopCount, onLoopCountChange, playNext]);

  /**
   * Setup media session controls
   */
  useEffect(() => {
    mediaSessionApi.setup({
      onPlay: () => {
        if (!isPlaying) togglePlayPause();
      },
      onPause: () => {
        if (isPlaying) togglePlayPause();
      },
      onNext: playNext,
      onPrevious: playPrevious,
      onSeekForward: seekForward,
      onSeekBackward: seekBackward
    });
  }, [isPlaying, togglePlayPause, playNext, playPrevious, seekForward, seekBackward]);

  /**
   * Update media session metadata when video changes
   */
  useEffect(() => {
    if (currentVideo) {
      mediaSessionApi.updateMetadata(currentVideo);
    }
  }, [currentVideo]);

  /**
   * Update tray tooltip when video or playing state changes
   */
  useEffect(() => {
    updateTrayTooltip();
  }, [updateTrayTooltip]);

  return {
    isPlaying,
    volume,
    isMuted,
    playerRef,
    togglePlayPause,
    playNext,
    playPrevious,
    seekForward,
    seekBackward,
    toggleMute,
    setPlayerVolume,
    handleLoopClick,
    onPlayerReady,
    onPlayerStateChange
  };
};
