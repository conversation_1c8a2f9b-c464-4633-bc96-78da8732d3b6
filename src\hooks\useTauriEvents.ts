import { useEffect } from 'react';
import { eventApi } from '../utils/tauri';

interface UseTauriEventsProps {
  onPlayPause: () => void;
  onNext: () => void;
  onPrevious: () => void;
  onShowQuitConfirmation: () => void;
}

/**
 * Custom hook for managing Tauri event listeners
 */
export const useTauriEvents = ({
  onPlayPause,
  onNext,
  onPrevious,
  onShowQuitConfirmation
}: UseTauriEventsProps) => {
  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const setupListeners = async () => {
      cleanup = await eventApi.setupTrayListeners({
        onPlayPause,
        onNext,
        onPrevious,
        onShowQuitConfirmation
      });
    };

    setupListeners();

    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [onPlayPause, onNext, onPrevious, onShowQuitConfirmation]);
};
