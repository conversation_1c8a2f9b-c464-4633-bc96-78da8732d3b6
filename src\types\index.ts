// Core data types for Lightning Shuffler

export interface Video {
  id: string;
  title: string;
  author: string;
  thumbnail: string;
  duration: string;
}

export interface Playlist {
  id: string;
  title: string;
  author: string;
  thumbnail: string;
  url: string;
  videos: Video[];
  video_count: number;
  created_at: string;
}

export interface Mix {
  id: string;
  name: string;
  playlist_ids: string[];
  created_at: string;
}

export interface AppData {
  playlists: Record<string, Playlist>;
  mixes: Record<string, Mix>;
  current_queue: Video[];
  current_index: number;
  shuffle_enabled: boolean;
  loop_count: number;
  volume: number;
}

export interface NotificationState {
  message: string;
  type: 'success' | 'error';
}

export interface AppState {
  appData: AppData | null;
  currentVideo: Video | null;
  isPlaying: boolean;
  searchQuery: string;
  playlistUrl: string;
  isLoading: boolean;
  showHelp: boolean;
  loopCount: number;
  volume: number;
  filteredVideos: Video[];
  notification: NotificationState | null;
  showQuitConfirmation: boolean;
}

// YouTube Player types
export interface YouTubePlayerEvent {
  target: YouTubePlayer;
  data: number;
}

export interface YouTubePlayer {
  playVideo(): void;
  pauseVideo(): void;
  getCurrentTime(): number;
  seekTo(seconds: number): void;
  setVolume(volume: number): void;
  isMuted(): boolean;
  mute(): void;
  unMute(): void;
}

// Tauri command types
export type TauriCommand = 
  | 'get_app_data'
  | 'save_app_data'
  | 'extract_playlist_id'
  | 'fetch_playlist_data'
  | 'add_playlist'
  | 'remove_playlist'
  | 'create_mix'
  | 'set_current_queue'
  | 'set_current_index'
  | 'update_tray_tooltip'
  | 'show_window'
  | 'hide_window'
  | 'show_quit_confirmation'
  | 'exit_app';

// Event types
export type TauriEvent = 
  | 'tray-play-pause'
  | 'tray-next'
  | 'tray-previous'
  | 'show-quit-confirmation';

// Component prop types
export interface VideoItemProps {
  video: Video;
  isCurrentVideo: boolean;
  onClick: (video: Video) => void;
  index: number;
}

export interface PlaylistItemProps {
  playlist: Playlist;
  onPlay: (playlist: Playlist) => void;
  onRefresh: (playlistId: string) => void;
  onDelete: (playlistId: string) => void;
  index: number;
}

export interface MixItemProps {
  mix: Mix;
  playlists: Record<string, Playlist>;
  onPlay: (mix: Mix) => void;
  onEdit: (mixId: string) => void;
  onDelete: (mixId: string) => void;
  index: number;
}

export interface ControlsProps {
  isPlaying: boolean;
  loopCount: number;
  volume: number;
  onPlayPause: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onShuffle: () => void;
  onLoopClick: (e: React.MouseEvent) => void;
  onVolumeChange: (volume: number) => void;
}

export interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export interface VideoPlayerProps {
  currentVideo: Video | null;
  onReady: (event: any) => void;
  onStateChange: (event: YouTubePlayerEvent) => void;
}

export interface SidebarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  playlists: Record<string, Playlist>;
  mixes: Record<string, Mix>;
  filteredVideos: Video[];
  currentVideo: Video | null;
  onPlayPlaylist: (playlist: Playlist) => void;
  onPlayMix: (mix: Mix) => void;
  onRefreshPlaylist: (playlistId: string) => void;
  onDeletePlaylist: (playlistId: string) => void;
  onDeleteMix: (mixId: string) => void;
  onVideoClick: (video: Video) => void;
}

export interface HeaderProps {
  playlistUrl: string;
  isLoading: boolean;
  onPlaylistUrlChange: (url: string) => void;
  onAddPlaylist: () => void;
  onShowHelp: () => void;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

export interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface QuitConfirmationProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export interface NotificationProps {
  notification: NotificationState | null;
}
