import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { AppData, Playlist, Video, Mix, TauriCommand, TauriEvent } from '../types';

/**
 * Wrapper for <PERSON><PERSON> invoke commands with error handling
 */
const invokeCommand = async <T>(command: TauriCommand, args?: Record<string, any>): Promise<T> => {
  try {
    return await invoke<T>(command, args);
  } catch (error) {
    console.error(`Failed to execute command ${command}:`, error);
    throw error;
  }
};

/**
 * App data operations
 */
export const appDataApi = {
  /**
   * Load app data from backend
   */
  load: async (): Promise<AppData> => {
    return invokeCommand<AppData>('get_app_data');
  },

  /**
   * Save app data to backend
   */
  save: async (): Promise<void> => {
    return invokeCommand<void>('save_app_data');
  }
};

/**
 * Playlist operations
 */
export const playlistApi = {
  /**
   * Extract playlist ID from URL
   */
  extractId: async (url: string): Promise<string> => {
    return invokeCommand<string>('extract_playlist_id', { url });
  },

  /**
   * Add a new playlist
   */
  add: async (playlist: Playlist): Promise<void> => {
    return invokeCommand<void>('add_playlist', { playlist });
  },

  /**
   * Remove a playlist
   */
  remove: async (playlistId: string): Promise<void> => {
    return invokeCommand<void>('remove_playlist', { playlistId });
  },

  /**
   * Fetch playlist data (legacy - now handled in frontend)
   */
  fetch: async (playlistId: string): Promise<Playlist> => {
    return invokeCommand<Playlist>('fetch_playlist_data', { playlistId });
  }
};

/**
 * Mix operations
 */
export const mixApi = {
  /**
   * Create a new mix
   */
  create: async (name: string, playlistIds: string[]): Promise<string> => {
    return invokeCommand<string>('create_mix', { name, playlistIds });
  }
};

/**
 * Queue operations
 */
export const queueApi = {
  /**
   * Set the current queue
   */
  set: async (videos: Video[], shuffle: boolean): Promise<void> => {
    return invokeCommand<void>('set_current_queue', { videos, shuffle });
  },

  /**
   * Set the current index
   */
  setIndex: async (index: number): Promise<void> => {
    return invokeCommand<void>('set_current_index', { index });
  }
};

/**
 * System tray operations
 */
export const trayApi = {
  /**
   * Update tray tooltip
   */
  updateTooltip: async (tooltip: string): Promise<void> => {
    return invokeCommand<void>('update_tray_tooltip', { tooltip });
  }
};

/**
 * Window operations
 */
export const windowApi = {
  /**
   * Show the main window
   */
  show: async (): Promise<void> => {
    return invokeCommand<void>('show_window');
  },

  /**
   * Hide the main window
   */
  hide: async (): Promise<void> => {
    return invokeCommand<void>('hide_window');
  },

  /**
   * Show quit confirmation dialog
   */
  showQuitConfirmation: async (): Promise<void> => {
    return invokeCommand<void>('show_quit_confirmation');
  },

  /**
   * Exit the application
   */
  exit: async (): Promise<void> => {
    return invokeCommand<void>('exit_app');
  }
};

/**
 * Event listener utilities
 */
export const eventApi = {
  /**
   * Listen to a Tauri event
   */
  listen: async <T>(event: TauriEvent, handler: (payload: T) => void) => {
    return await listen(event, (event) => {
      handler(event.payload as T);
    });
  },

  /**
   * Setup all tray event listeners
   */
  setupTrayListeners: async (handlers: {
    onPlayPause: () => void;
    onNext: () => void;
    onPrevious: () => void;
    onShowQuitConfirmation: () => void;
  }) => {
    const unlisteners = await Promise.all([
      eventApi.listen('tray-play-pause', handlers.onPlayPause),
      eventApi.listen('tray-next', handlers.onNext),
      eventApi.listen('tray-previous', handlers.onPrevious),
      eventApi.listen('show-quit-confirmation', handlers.onShowQuitConfirmation)
    ]);

    // Return cleanup function
    return () => {
      unlisteners.forEach(unlisten => unlisten.then(f => f()));
    };
  }
};

/**
 * Media session utilities
 */
export const mediaSessionApi = {
  /**
   * Setup media session handlers
   */
  setup: (handlers: {
    onPlay: () => void;
    onPause: () => void;
    onNext: () => void;
    onPrevious: () => void;
    onSeekForward: () => void;
    onSeekBackward: () => void;
  }) => {
    if ('mediaSession' in navigator) {
      navigator.mediaSession.setActionHandler('play', handlers.onPlay);
      navigator.mediaSession.setActionHandler('pause', handlers.onPause);
      navigator.mediaSession.setActionHandler('nexttrack', handlers.onNext);
      navigator.mediaSession.setActionHandler('previoustrack', handlers.onPrevious);
      navigator.mediaSession.setActionHandler('seekforward', handlers.onSeekForward);
      navigator.mediaSession.setActionHandler('seekbackward', handlers.onSeekBackward);
    }
  },

  /**
   * Update media session metadata
   */
  updateMetadata: (video: Video) => {
    if ('mediaSession' in navigator) {
      navigator.mediaSession.metadata = new MediaMetadata({
        title: video.title,
        artist: video.author,
        artwork: [
          {
            src: video.thumbnail,
            sizes: '320x180',
            type: 'image/jpeg'
          }
        ]
      });
    }
  },

  /**
   * Update playback state
   */
  updatePlaybackState: (playing: boolean) => {
    if ('mediaSession' in navigator) {
      navigator.mediaSession.playbackState = playing ? 'playing' : 'paused';
    }
  }
};

/**
 * Keyboard shortcut utilities
 */
export const keyboardApi = {
  /**
   * Setup keyboard event listeners
   */
  setup: (handlers: {
    onPlayPause: () => void;
    onSeekForward: () => void;
    onSeekBackward: () => void;
    onMute: () => void;
    onShowHelp: () => void;
  }) => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Don't handle shortcuts when typing in input fields
      if (e.target instanceof HTMLInputElement) return;

      switch (e.key.toLowerCase()) {
        case ' ':
        case 'k':
          e.preventDefault();
          handlers.onPlayPause();
          break;
        case 'arrowleft':
          e.preventDefault();
          handlers.onSeekBackward();
          break;
        case 'arrowright':
          e.preventDefault();
          handlers.onSeekForward();
          break;
        case 'm':
          e.preventDefault();
          handlers.onMute();
          break;
        case 'h':
          e.preventDefault();
          handlers.onShowHelp();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    
    // Return cleanup function
    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }
};
