import { Playlist, Video } from '../types';

/**
 * Formats duration from various formats to MM:SS
 */
export const formatDuration = (duration: any): string => {
  if (!duration) return '0:00';

  // If duration is already a string, return it
  if (typeof duration === 'string') return duration;

  // If duration is in milliseconds, convert to seconds
  if (typeof duration === 'number') {
    const totalSeconds = Math.floor(duration / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  return '0:00';
};

/**
 * Fetches YouTube playlist data using youtube-sr
 */
export const fetchYouTubePlaylist = async (playlistId: string): Promise<Playlist> => {
  try {
    console.log('Fetching playlist with ID:', playlistId);

    // Use youtube-sr to fetch playlist data
    const YouTubeSr = await import('youtube-sr').then(m => m.default);
    
    // Get playlist info
    const playlist = await YouTubeSr.getPlaylist(`https://www.youtube.com/playlist?list=${playlistId}`);
    
    if (!playlist || !playlist.videos || playlist.videos.length === 0) {
      throw new Error('Playlist not found or is empty');
    }

    // Map the videos to our format
    const videos: Video[] = playlist.videos.map(video => ({
      id: video.id || '',
      title: video.title || 'Unknown Title',
      author: video.channel?.name || 'Unknown Channel',
      thumbnail: video.thumbnail?.url || `https://img.youtube.com/vi/${video.id}/mqdefault.jpg`,
      duration: formatDuration(video.duration || 0)
    }));

    return {
      id: playlistId,
      title: playlist.title || `Playlist ${playlistId}`,
      author: playlist.channel?.name || 'Unknown Author',
      thumbnail: playlist.thumbnail?.url || (videos[0]?.thumbnail || ''),
      url: `https://www.youtube.com/playlist?list=${playlistId}`,
      videos: videos,
      video_count: videos.length,
      created_at: new Date().toISOString()
    };
  } catch (error) {
    console.error('YouTube fetch error:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found') || error.message.includes('empty')) {
        throw new Error('Playlist not found or is empty. Please check the URL.');
      }
    }
    throw new Error('Failed to fetch playlist. Please check the URL and try again.');
  }
};

/**
 * Extracts playlist IDs from a comma-separated list of URLs
 */
export const extractPlaylistIds = (urls: string): string[] => {
  const urlList = urls.split(',').map(url => url.trim()).filter(url => url.length > 0);
  const playlistIds: string[] = [];
  
  for (const url of urlList) {
    try {
      const playlistRegex = /[&?]list=([a-zA-Z0-9_-]+)/;
      const match = url.match(playlistRegex);
      if (match && match[1]) {
        playlistIds.push(match[1]);
      }
    } catch (error) {
      console.warn('Failed to extract playlist ID from URL:', url);
    }
  }
  
  return playlistIds;
};

/**
 * Validates if a string contains valid YouTube playlist URLs
 */
export const validatePlaylistUrls = (urls: string): boolean => {
  const urlList = urls.split(',').map(url => url.trim()).filter(url => url.length > 0);
  
  if (urlList.length === 0) return false;
  
  const playlistRegex = /[&?]list=([a-zA-Z0-9_-]+)/;
  
  return urlList.every(url => playlistRegex.test(url));
};

/**
 * Combines videos from multiple playlists for a mix
 */
export const combinePlaylistsForMix = (
  playlistIds: string[], 
  playlists: Record<string, Playlist>
): Video[] => {
  const allVideos: Video[] = [];
  
  for (const playlistId of playlistIds) {
    const playlist = playlists[playlistId];
    if (playlist && playlist.videos) {
      allVideos.push(...playlist.videos);
    }
  }
  
  return allVideos;
};

/**
 * Shuffles an array using Fisher-Yates algorithm
 */
export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

/**
 * Filters videos based on search query (title and author)
 */
export const filterVideos = (videos: Video[], searchQuery: string): Video[] => {
  if (!searchQuery.trim()) return videos;
  
  const query = searchQuery.toLowerCase();
  return videos.filter(video =>
    video.title.toLowerCase().includes(query) ||
    video.author.toLowerCase().includes(query)
  );
};

/**
 * Gets the next video index in a queue
 */
export const getNextVideoIndex = (currentIndex: number, queueLength: number): number => {
  return (currentIndex + 1) % queueLength;
};

/**
 * Gets the previous video index in a queue
 */
export const getPreviousVideoIndex = (currentIndex: number, queueLength: number): number => {
  return currentIndex === 0 ? queueLength - 1 : currentIndex - 1;
};
